# 群组成员扫描功能

## 功能概述

新增的 `/scan_member` 命令可以扫描群组内所有成员（除了机器人）的信息，并将数据存储到 `tg_syl_data` 数据库中。

## 功能特性

### 🔍 成员扫描
- 扫描群组内所有成员信息
- 自动排除机器人用户（可选）
- 获取用户详细信息（用户名、姓名、语言等）
- 记录群组相关信息
- **记录成员加入群组的时间**

### 💾 数据存储
- 使用 MySQL 数据库存储成员数据
- 自动创建数据库表结构
- 支持数据更新和去重
- 记录扫描历史和统计信息

### 📊 统计功能
- 实时显示扫描进度
- 统计普通用户和机器人数量
- 记录扫描耗时
- 保存扫描结果到数据库

## 使用方法

### 基本使用

1. **在群组中使用命令**
   ```
   /scan_member
   ```

2. **权限要求**
   - 机器人需要管理员权限
   - 只能在群组或超级群组中使用
   - 需要有获取成员列表的权限

### 扫描过程

1. **开始扫描**
   - 机器人检查权限
   - 显示群组基本信息
   - 开始获取成员列表

2. **进度显示**
   - 每处理50个成员更新一次进度
   - 显示已处理的成员数量
   - 实时统计用户类型

3. **数据保存**
   - 将成员信息保存到数据库
   - 记录扫描结果和统计信息
   - 显示最终扫描报告

4. **自动清理**
   - 5分钟后自动删除结果消息
   - 避免群组消息过多

## 数据库结构

### 群组成员表 (group_members)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| user_id | BIGINT | 用户ID |
| group_id | BIGINT | 群组ID |
| username | VARCHAR(255) | 用户名 |
| first_name | VARCHAR(255) | 名字 |
| last_name | VARCHAR(255) | 姓氏 |
| user_type | ENUM | 用户类型 |
| is_bot | BOOLEAN | 是否为机器人 |
| language_code | VARCHAR(10) | 语言代码 |
| is_premium | BOOLEAN | 是否为Premium用户 |
| group_title | VARCHAR(255) | 群组标题 |
| group_username | VARCHAR(255) | 群组用户名 |
| group_type | VARCHAR(50) | 群组类型 |
| member_status | VARCHAR(50) | 成员状态 |
| joined_date | DATETIME | 加入群组时间 |
| scan_time | DATETIME | 扫描时间 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

### 扫描记录表 (member_scan_records)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| group_id | BIGINT | 群组ID |
| group_title | VARCHAR(255) | 群组标题 |
| total_members | INT | 总成员数 |
| scanned_members | INT | 扫描到的成员数 |
| regular_users | INT | 普通用户数 |
| bots | INT | 机器人数 |
| scan_time | DATETIME | 扫描时间 |
| scan_duration | FLOAT | 扫描耗时（秒） |
| success | BOOLEAN | 是否成功 |
| error_message | TEXT | 错误信息 |
| created_at | DATETIME | 创建时间 |

## 技术实现

### 核心组件

1. **用户数据模型** (`src/models/user.py`)
   - `GroupMember`: 群组成员数据结构
   - `ScanResult`: 扫描结果数据结构
   - `UserType`: 用户类型枚举

2. **用户管理器** (`src/core/user_manager.py`)
   - 数据库连接池管理
   - 成员数据的增删改查
   - 扫描记录管理
   - 统计信息查询

3. **命令处理器** (`src/handlers/member_handler.py`)
   - `/scan_member` 命令处理
   - 权限检查和错误处理
   - 进度显示和结果反馈
   - 自动消息清理

### 特性说明

- **数据去重**: 使用 `(user_id, group_id)` 作为唯一键
- **增量更新**: 支持重复扫描，自动更新用户信息
- **加入时间记录**: 尝试获取并记录成员加入群组的时间
- **错误处理**: 完善的异常处理和错误提示
- **性能优化**: 批量数据操作，连接池管理
- **用户体验**: 实时进度显示，自动消息清理
- **数据库迁移**: 自动检查和更新表结构

## 使用示例

### 成功扫描示例

```
✅ 群组成员扫描完成

📊 群组信息：
• 群组名称: 测试群组
• 群组ID: -1001234567890
• 群组类型: supergroup

👥 扫描结果：
• 总成员数: 150
• 成功扫描: 148
• 普通用户: 145
• 机器人: 3

⏱️ 扫描耗时: 25.6 秒
💾 数据已保存到数据库（包含加入时间）
```

### 错误处理示例

```
❌ 群组成员扫描失败

📊 群组信息：
• 群组名称: 测试群组
• 群组ID: -1001234567890

❗ 错误信息：
机器人没有权限获取群组成员列表

⏱️ 扫描耗时: 2.1 秒
```

## 注意事项

1. **权限要求**
   - 机器人必须是群组管理员
   - 需要有获取成员列表的权限

2. **性能考虑**
   - 大群组扫描可能需要较长时间
   - 建议在成员较少时进行扫描

3. **隐私保护**
   - 只获取公开的用户信息
   - 遵守Telegram的使用条款

4. **数据管理**
   - 定期清理过期的扫描记录
   - 注意数据库存储空间

## 扩展功能

未来可以考虑添加的功能：

- 定时自动扫描
- 成员变化监控
- 数据导出功能
- 成员活跃度分析
- 群组对比功能

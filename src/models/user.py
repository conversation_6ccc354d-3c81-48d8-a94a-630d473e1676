"""
用户数据模型

定义群组成员的数据结构
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum


class UserType(Enum):
    """用户类型枚举"""
    REGULAR = "regular"      # 普通用户
    BOT = "bot"             # 机器人
    ADMIN = "admin"         # 管理员
    CREATOR = "creator"     # 群主


@dataclass
class GroupMember:
    """群组成员数据模型"""
    
    user_id: int                                    # 用户ID
    group_id: int                                   # 群组ID
    username: Optional[str] = None                  # 用户名（@username）
    first_name: Optional[str] = None                # 名字
    last_name: Optional[str] = None                 # 姓氏
    user_type: UserType = UserType.REGULAR          # 用户类型
    is_bot: bool = False                           # 是否为机器人
    language_code: Optional[str] = None             # 语言代码
    is_premium: bool = False                       # 是否为Premium用户
    added_to_attachment_menu: bool = False          # 是否添加到附件菜单
    can_join_groups: bool = True                   # 是否可以加入群组
    can_read_all_group_messages: bool = False      # 是否可以读取所有群组消息
    supports_inline_queries: bool = False          # 是否支持内联查询
    # 群组相关信息
    group_title: Optional[str] = None              # 群组标题
    group_username: Optional[str] = None           # 群组用户名
    group_type: Optional[str] = None               # 群组类型
    member_status: Optional[str] = None            # 成员状态
    joined_date: Optional[datetime] = None         # 加入群组时间
    # 时间戳
    scan_time: datetime = field(default_factory=datetime.now)  # 扫描时间
    created_at: datetime = field(default_factory=datetime.now) # 创建时间
    updated_at: datetime = field(default_factory=datetime.now) # 更新时间
    
    def __post_init__(self):
        """初始化后处理"""
        # 根据is_bot设置用户类型
        if self.is_bot:
            self.user_type = UserType.BOT
    
    @property
    def display_name(self) -> str:
        """获取显示名称"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        elif self.username:
            return f"@{self.username}"
        else:
            return f"User_{self.user_id}"
    
    @property
    def full_username(self) -> str:
        """获取完整用户名"""
        if self.username:
            return f"@{self.username}"
        return ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'user_id': self.user_id,
            'group_id': self.group_id,
            'username': self.username,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'user_type': self.user_type.value,
            'is_bot': self.is_bot,
            'language_code': self.language_code,
            'is_premium': self.is_premium,
            'added_to_attachment_menu': self.added_to_attachment_menu,
            'can_join_groups': self.can_join_groups,
            'can_read_all_group_messages': self.can_read_all_group_messages,
            'supports_inline_queries': self.supports_inline_queries,
            'group_title': self.group_title,
            'group_username': self.group_username,
            'group_type': self.group_type,
            'member_status': self.member_status,
            'joined_date': self.joined_date.isoformat() if self.joined_date else None,
            'scan_time': self.scan_time.isoformat(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GroupMember':
        """从字典创建实例"""
        return cls(
            user_id=data['user_id'],
            group_id=data['group_id'],
            username=data.get('username'),
            first_name=data.get('first_name'),
            last_name=data.get('last_name'),
            user_type=UserType(data.get('user_type', 'regular')),
            is_bot=data.get('is_bot', False),
            language_code=data.get('language_code'),
            is_premium=data.get('is_premium', False),
            added_to_attachment_menu=data.get('added_to_attachment_menu', False),
            can_join_groups=data.get('can_join_groups', True),
            can_read_all_group_messages=data.get('can_read_all_group_messages', False),
            supports_inline_queries=data.get('supports_inline_queries', False),
            group_title=data.get('group_title'),
            group_username=data.get('group_username'),
            group_type=data.get('group_type'),
            member_status=data.get('member_status'),
            joined_date=datetime.fromisoformat(data['joined_date']) if data.get('joined_date') else None,
            scan_time=datetime.fromisoformat(data['scan_time']) if data.get('scan_time') else datetime.now(),
            created_at=datetime.fromisoformat(data['created_at']) if data.get('created_at') else datetime.now(),
            updated_at=datetime.fromisoformat(data['updated_at']) if data.get('updated_at') else datetime.now()
        )
    
    @classmethod
    def from_telegram_user(cls, user, group_id: int, group_title: str = None,
                          group_username: str = None, group_type: str = None,
                          member_status: str = None, joined_date: datetime = None) -> 'GroupMember':
        """从Telegram用户对象创建实例"""
        return cls(
            user_id=user.id,
            group_id=group_id,
            username=user.username,
            first_name=user.first_name,
            last_name=user.last_name,
            is_bot=user.is_bot,
            language_code=user.language_code,
            is_premium=getattr(user, 'is_premium', False),
            added_to_attachment_menu=getattr(user, 'added_to_attachment_menu', False),
            can_join_groups=getattr(user, 'can_join_groups', True),
            can_read_all_group_messages=getattr(user, 'can_read_all_group_messages', False),
            supports_inline_queries=getattr(user, 'supports_inline_queries', False),
            group_title=group_title,
            group_username=group_username,
            group_type=group_type,
            member_status=member_status,
            joined_date=joined_date
        )


@dataclass
class ScanResult:
    """扫描结果数据模型"""
    
    group_id: int                           # 群组ID
    group_title: str                        # 群组标题
    total_members: int                      # 总成员数
    scanned_members: int                    # 扫描到的成员数
    regular_users: int                      # 普通用户数
    bots: int                              # 机器人数
    scan_time: datetime                     # 扫描时间
    scan_duration: float                    # 扫描耗时（秒）
    success: bool = True                    # 是否成功
    error_message: Optional[str] = None     # 错误信息
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'group_id': self.group_id,
            'group_title': self.group_title,
            'total_members': self.total_members,
            'scanned_members': self.scanned_members,
            'regular_users': self.regular_users,
            'bots': self.bots,
            'scan_time': self.scan_time.isoformat(),
            'scan_duration': self.scan_duration,
            'success': self.success,
            'error_message': self.error_message
        }

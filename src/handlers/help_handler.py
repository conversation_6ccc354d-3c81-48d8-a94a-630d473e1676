"""
帮助命令处理器

提供机器人使用帮助和命令列表
"""

from telegram import Update
from telegram.ext import ContextTypes
from src.core.decorators import command_handler
from src.core.logger import default_logger as logger


@command_handler("help", "显示帮助信息", priority=10)
async def handle_help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理帮助命令"""
    message = update.message
    if not message:
        return
    
    chat_type = message.chat.type
    
    if chat_type in ['group', 'supergroup']:
        # 群组帮助信息
        help_text = (
            "🤖 **机器人群组命令帮助**\n\n"
            "📚 **老师标签功能：**\n"
            "• `/search_tag 标签1,标签2` - 搜索老师标签\n"
            "• `/list_teachers` - 查看所有老师\n\n"
            "🎉 **抽奖功能：**\n"
            "• 发送抽奖口令参与抽奖\n"
            "• 等待开奖时间到达自动开奖\n\n"
            "💡 **提示：**\n"
            "• 创建抽奖请私聊机器人使用 `/create` 命令\n"
            "• 群组消息会在一定时间后自动删除"
        )
        
        # 发送帮助信息
        help_message = await message.reply_text(help_text, parse_mode='Markdown')
        
        # 30秒后自动删除帮助消息和用户命令
        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, help_message.message_id
            ),
            when=30  # 30秒
        )

        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, message.message_id
            ),
            when=30  # 30秒
        )
        
    else:
        # 私聊帮助信息
        help_text = (
            "🤖 **机器人私聊命令帮助**\n\n"
            "🎉 **抽奖功能：**\n"
            "• `/create` - 创建抽奖活动\n"
            "• `/my_lotteries` - 查看我的抽奖\n"
            "• `/stop_lottery` - 取消抽奖活动\n"
            "• `/lottery_detail <活动ID>` - 查看抽奖详情\n"
            "• `/lottery_stats` - 抽奖统计\n\n"
            "📚 **老师管理功能：**\n"
            "• `/add_teacher` - 添加老师\n"
            "• `/list_teachers` - 查看所有老师\n"
            "• `/list_tags` - 查看所有标签\n\n"
            "💡 **使用说明：**\n"
            "1. 创建抽奖后会生成口令和目标群组\n"
            "2. 在目标群组发送口令即可参与抽奖\n"
            "3. 到达开奖时间会自动开奖并通知中奖者"
        )
        
        # 发送帮助信息
        help_message = await message.reply_text(help_text, parse_mode='Markdown')

        # 30秒后自动删除帮助消息和用户命令
        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, help_message.message_id
            ),
            when=30  # 30秒
        )

        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, message.message_id
            ),
            when=30  # 30秒
        )

    logger.info(f"用户 {message.from_user.id} 在 {chat_type} 中查看了帮助信息")


@command_handler("start", "开始使用机器人", priority=10)
async def handle_start_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理开始命令"""
    message = update.message
    if not message:
        return
    
    # 只在私聊中响应start命令
    if message.chat.type != 'private':
        return
    
    welcome_text = (
        f"👋 欢迎使用机器人，{message.from_user.first_name}！\n\n"
        "🎉 **主要功能：**\n"
        "• 创建和管理抽奖活动\n"
        "• 老师标签搜索功能\n\n"
        "📖 **快速开始：**\n"
        "• 使用 `/help` 查看所有命令\n"
        "• 使用 `/create` 创建抽奖活动\n"
        "• 将机器人添加到群组使用群组功能\n\n"
        "💡 **提示：** 输入 `/` 可以看到所有可用命令"
    )
    
    await message.reply_text(welcome_text, parse_mode='Markdown')
    logger.info(f"用户 {message.from_user.id} 开始使用机器人")


async def delete_message_by_id(context, chat_id: int, message_id: int):
    """删除指定消息"""
    try:
        await context.bot.delete_message(chat_id=chat_id, message_id=message_id)
        logger.debug(f"已删除消息 {message_id} (群组 {chat_id})")
    except Exception as e:
        logger.debug(f"删除消息失败 {message_id} (群组 {chat_id}): {e}")

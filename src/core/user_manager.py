"""
用户管理器

管理群组成员数据的数据库操作
"""

import time
from typing import List, Optional, Dict, Any
from contextlib import contextmanager
from mysql.connector import pooling
from datetime import datetime

from src.core.logger import default_logger as logger
from src.models.user import GroupMember, ScanResult, UserType
from database.mysql_config import get_mysql_pool_config


class UserManager:
    """用户管理器"""
    
    def __init__(self):
        self.connection_pool = None
        self._init_connection_pool()
        self._ensure_tables_exist()
    
    def _init_connection_pool(self):
        """初始化数据库连接池"""
        try:
            pool_config = get_mysql_pool_config()
            self.connection_pool = pooling.MySQLConnectionPool(**pool_config)
            logger.info("用户管理器数据库连接池初始化成功")
        except Exception as e:
            logger.error(f"初始化用户管理器数据库连接池失败: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        connection = None
        try:
            connection = self.connection_pool.get_connection()
            yield connection
        except Exception as e:
            if connection:
                connection.rollback()
            logger.error(f"用户管理器数据库操作出错: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    def _ensure_tables_exist(self):
        """确保数据库表存在"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 创建群组成员表
                create_members_table = """
                CREATE TABLE IF NOT EXISTS group_members (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY,
                    user_id BIGINT NOT NULL,
                    group_id BIGINT NOT NULL,
                    username VARCHAR(255),
                    first_name VARCHAR(255),
                    last_name VARCHAR(255),
                    user_type ENUM('regular', 'bot', 'admin', 'creator') DEFAULT 'regular',
                    is_bot BOOLEAN DEFAULT FALSE,
                    language_code VARCHAR(10),
                    is_premium BOOLEAN DEFAULT FALSE,
                    added_to_attachment_menu BOOLEAN DEFAULT FALSE,
                    can_join_groups BOOLEAN DEFAULT TRUE,
                    can_read_all_group_messages BOOLEAN DEFAULT FALSE,
                    supports_inline_queries BOOLEAN DEFAULT FALSE,
                    group_title VARCHAR(255),
                    group_username VARCHAR(255),
                    group_type VARCHAR(50),
                    member_status VARCHAR(50),
                    joined_date DATETIME,
                    scan_time DATETIME NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_user_group (user_id, group_id),
                    INDEX idx_group_id (group_id),
                    INDEX idx_user_id (user_id),
                    INDEX idx_scan_time (scan_time),
                    INDEX idx_user_type (user_type),
                    INDEX idx_joined_date (joined_date)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """
                
                # 创建扫描记录表
                create_scan_records_table = """
                CREATE TABLE IF NOT EXISTS member_scan_records (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY,
                    group_id BIGINT NOT NULL,
                    group_title VARCHAR(255),
                    total_members INT DEFAULT 0,
                    scanned_members INT DEFAULT 0,
                    regular_users INT DEFAULT 0,
                    bots INT DEFAULT 0,
                    scan_time DATETIME NOT NULL,
                    scan_duration FLOAT DEFAULT 0,
                    success BOOLEAN DEFAULT TRUE,
                    error_message TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_group_id (group_id),
                    INDEX idx_scan_time (scan_time)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """
                
                cursor.execute(create_members_table)
                cursor.execute(create_scan_records_table)

                # 检查并添加 joined_date 列（如果不存在）
                self._add_joined_date_column_if_not_exists(cursor)

                conn.commit()
                cursor.close()

                logger.info("用户管理器数据库表检查完成")
                
        except Exception as e:
            logger.error(f"创建用户管理器数据库表失败: {e}")
            raise

    def _add_joined_date_column_if_not_exists(self, cursor):
        """检查并添加 joined_date 列（如果不存在）"""
        try:
            # 检查列是否存在
            cursor.execute("""
                SELECT COUNT(*)
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = 'group_members'
                AND COLUMN_NAME = 'joined_date'
            """)

            column_exists = cursor.fetchone()[0] > 0

            if not column_exists:
                # 添加 joined_date 列
                cursor.execute("""
                    ALTER TABLE group_members
                    ADD COLUMN joined_date DATETIME AFTER member_status
                """)

                # 添加索引
                cursor.execute("""
                    ALTER TABLE group_members
                    ADD INDEX idx_joined_date (joined_date)
                """)

                logger.info("已添加 joined_date 列到 group_members 表")
            else:
                logger.debug("joined_date 列已存在")

        except Exception as e:
            logger.warning(f"检查/添加 joined_date 列时出错: {e}")
            # 不抛出异常，因为这不是致命错误
    
    def save_members(self, members: List[GroupMember]) -> bool:
        """批量保存群组成员"""
        if not members:
            return True
            
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 使用 INSERT ... ON DUPLICATE KEY UPDATE 语句
                sql = """
                INSERT INTO group_members (
                    user_id, group_id, username, first_name, last_name, user_type, is_bot,
                    language_code, is_premium, added_to_attachment_menu, can_join_groups,
                    can_read_all_group_messages, supports_inline_queries, group_title,
                    group_username, group_type, member_status, joined_date, scan_time, created_at, updated_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                ) ON DUPLICATE KEY UPDATE
                    username = VALUES(username),
                    first_name = VALUES(first_name),
                    last_name = VALUES(last_name),
                    user_type = VALUES(user_type),
                    language_code = VALUES(language_code),
                    is_premium = VALUES(is_premium),
                    added_to_attachment_menu = VALUES(added_to_attachment_menu),
                    can_join_groups = VALUES(can_join_groups),
                    can_read_all_group_messages = VALUES(can_read_all_group_messages),
                    supports_inline_queries = VALUES(supports_inline_queries),
                    group_title = VALUES(group_title),
                    group_username = VALUES(group_username),
                    group_type = VALUES(group_type),
                    member_status = VALUES(member_status),
                    joined_date = COALESCE(joined_date, VALUES(joined_date)),
                    scan_time = VALUES(scan_time),
                    updated_at = VALUES(updated_at)
                """
                
                # 准备数据
                data = []
                for member in members:
                    data.append((
                        member.user_id, member.group_id, member.username, member.first_name,
                        member.last_name, member.user_type.value, member.is_bot,
                        member.language_code, member.is_premium, member.added_to_attachment_menu,
                        member.can_join_groups, member.can_read_all_group_messages,
                        member.supports_inline_queries, member.group_title, member.group_username,
                        member.group_type, member.member_status, member.joined_date,
                        member.scan_time, member.created_at, member.updated_at
                    ))
                
                cursor.executemany(sql, data)
                conn.commit()
                cursor.close()
                
                logger.info(f"成功保存 {len(members)} 个群组成员")
                return True
                
        except Exception as e:
            logger.error(f"保存群组成员失败: {e}")
            return False
    
    def save_scan_record(self, scan_result: ScanResult) -> bool:
        """保存扫描记录"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                sql = """
                INSERT INTO member_scan_records (
                    group_id, group_title, total_members, scanned_members, regular_users,
                    bots, scan_time, scan_duration, success, error_message
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                cursor.execute(sql, (
                    scan_result.group_id, scan_result.group_title, scan_result.total_members,
                    scan_result.scanned_members, scan_result.regular_users, scan_result.bots,
                    scan_result.scan_time, scan_result.scan_duration, scan_result.success,
                    scan_result.error_message
                ))
                
                conn.commit()
                cursor.close()
                
                logger.info(f"成功保存扫描记录: 群组 {scan_result.group_id}")
                return True
                
        except Exception as e:
            logger.error(f"保存扫描记录失败: {e}")
            return False
    
    def get_group_members(self, group_id: int, exclude_bots: bool = True) -> List[GroupMember]:
        """获取群组成员列表"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)
                
                sql = "SELECT * FROM group_members WHERE group_id = %s"
                params = [group_id]
                
                if exclude_bots:
                    sql += " AND is_bot = FALSE"
                
                sql += " ORDER BY scan_time DESC, user_id"
                
                cursor.execute(sql, params)
                results = cursor.fetchall()
                cursor.close()
                
                members = []
                for row in results:
                    # 处理日期时间字段
                    if isinstance(row['joined_date'], datetime):
                        row['joined_date'] = row['joined_date'].isoformat()
                    if isinstance(row['scan_time'], datetime):
                        row['scan_time'] = row['scan_time'].isoformat()
                    if isinstance(row['created_at'], datetime):
                        row['created_at'] = row['created_at'].isoformat()
                    if isinstance(row['updated_at'], datetime):
                        row['updated_at'] = row['updated_at'].isoformat()

                    members.append(GroupMember.from_dict(row))
                return members
                
        except Exception as e:
            logger.error(f"获取群组成员失败: {e}")
            return []
    
    def get_member_count(self, group_id: int, exclude_bots: bool = True) -> int:
        """获取群组成员数量"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                sql = "SELECT COUNT(*) FROM group_members WHERE group_id = %s"
                params = [group_id]
                
                if exclude_bots:
                    sql += " AND is_bot = FALSE"
                
                cursor.execute(sql, params)
                count = cursor.fetchone()[0]
                cursor.close()
                
                return count
                
        except Exception as e:
            logger.error(f"获取群组成员数量失败: {e}")
            return 0
    
    def get_scan_history(self, group_id: int, limit: int = 10) -> List[ScanResult]:
        """获取扫描历史记录"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)
                
                sql = """
                SELECT * FROM member_scan_records 
                WHERE group_id = %s 
                ORDER BY scan_time DESC 
                LIMIT %s
                """
                
                cursor.execute(sql, (group_id, limit))
                results = cursor.fetchall()
                cursor.close()
                
                scan_results = []
                for row in results:
                    scan_results.append(ScanResult(
                        group_id=row['group_id'],
                        group_title=row['group_title'],
                        total_members=row['total_members'],
                        scanned_members=row['scanned_members'],
                        regular_users=row['regular_users'],
                        bots=row['bots'],
                        scan_time=row['scan_time'],
                        scan_duration=row['scan_duration'],
                        success=row['success'],
                        error_message=row['error_message']
                    ))
                
                return scan_results
                
        except Exception as e:
            logger.error(f"获取扫描历史记录失败: {e}")
            return []


# 全局用户管理器实例
user_manager = UserManager()
